<?php

namespace Modules\Bank\Integrations\Ocb\Requests;

use Illuminate\Support\Str;
use Modules\Bank\Integrations\Ocb\OcbBaseRequest;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Traits\Body\HasJsonBody;

/**
 * Retrieve OCB Recipient Name Request
 *
 * This request is used to retrieve the account holder name for a given OCB account number.
 * It requires a separate access token obtained through AccessTokenForAccountLookupRequest.
 */
class RetrieveOcbRecipientNameRequest extends OcbBaseRequest implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(public string $accountNumber)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/v1/payment/retrieveOCBRecipientName';
    }

    protected function defaultHeaders(): array
    {
        return [
            'X-Client-Certificate' => config('bank.supported_banks.ocb.api_config.client_certificate'),
            'X-IBM-Client-Id' => config('bank.supported_banks.ocb.api_config.client_id'),
            'X-IBM-Client-Secret' => config('bank.supported_banks.ocb.api_config.client_secret'),
            'X-Signature' => $this->generateSignature($this->defaultBody()),
            'Content-Type' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return once(function () {
            return [
                'trace' => [
                    'clientTransId' => Str::of(Str::ulid())->lower()->toString(),
                    'clientTimestamp' => date('YmdHis000'),
                ],
                'data' => [
                    'queryInfo' => [
                        'accountNumber' => $this->accountNumber,
                    ],
                ],
            ];
        });
    }
}
