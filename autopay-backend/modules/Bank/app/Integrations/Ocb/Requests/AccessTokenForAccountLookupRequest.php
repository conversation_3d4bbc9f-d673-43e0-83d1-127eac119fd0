<?php

namespace Modules\Bank\Integrations\Ocb\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasFormBody;

/**
 * Access Token Request for OCB Account Lookup
 *
 * This request is used to obtain an access token specifically for account lookup operations.
 * It uses separate credentials from the main OCB integration to provide isolated access
 * for retrieving account holder names.
 */
class AccessTokenForAccountLookupRequest extends Request implements HasBody
{
    use HasFormBody;

    protected Method $method = Method::POST;

    public function resolveEndpoint(): string
    {
        return '/ocb-oauth-provider/oauth2/token';
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/x-www-form-urlencoded',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'grant_type' => 'password',
            'username' => config('bank.supported_banks.ocb.api_config.username'),
            'password' => config('bank.supported_banks.ocb.api_config.password'),
            'client_id' => config('bank.supported_banks.ocb.api_config.client_id'),
            'client_secret' => config('bank.supported_banks.ocb.api_config.client_secret'),
            'scope' => 'OCB',
        ];
    }
}
