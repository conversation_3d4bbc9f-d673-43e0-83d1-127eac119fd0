<?php

namespace Modules\Bank\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Bank\Factories\BankStrategyFactory;

class LookupAccountNameRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'accountNumber' => 'required|string|min:6|max:20',
            'bankCode' => 'required|in:'.implode(',', array_keys(BankStrategyFactory::getAllBanksConfig())),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'accountNumber.required' => 'Số tài khoản là bắt buộc.',
            'accountNumber.min' => 'Số tài khoản phải có ít nhất 6 ký tự.',
            'accountNumber.max' => 'Số tài khoản không được vượt quá 20 ký tự.',
            'bankCode.required' => 'Mã ngân hàng là bắt buộc.',
            'bankCode.in' => 'Mã ngân hàng không hợp lệ.',
        ];
    }
}
