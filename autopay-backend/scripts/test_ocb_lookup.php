<?php

/**
 * Manual test script for OCB Account Lookup functionality
 *
 * This script tests the OCB account lookup feature by making direct API calls
 * to verify the integration works correctly.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\Http;
use Modules\Bank\Strategies\OcbStrategy;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
$dotenv->load();

echo "=== OCB Account Lookup Test Script ===\n\n";

// Test configuration
$testAccountNumber = '**********'; // Replace with a valid test account number
$bankCode = 'ocb';

echo "Testing account number: {$testAccountNumber}\n";
echo "Bank code: {$bankCode}\n\n";

// Test 1: Direct API call to lookup endpoint
echo "1. Testing direct API call to lookup endpoint...\n";

try {
    $apiUrl = env('APP_URL', 'http://localhost:8000') . '/api/banks/lookup-account-name';

    $response = Http::post($apiUrl, [
        'accountNumber' => $testAccountNumber,
        'bankCode' => $bankCode
    ]);

    echo "Status: " . $response->status() . "\n";
    echo "Response: " . $response->body() . "\n\n";

    if ($response->successful()) {
        $data = $response->json();
        if ($data['success']) {
            echo "✅ Account lookup successful!\n";
            echo "Account Name: " . ($data['account_name'] ?? 'N/A') . "\n";
        } else {
            echo "❌ Account lookup failed: " . ($data['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ API call failed with status: " . $response->status() . "\n";
    }

} catch (Exception $e) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Test with invalid account number
echo "2. Testing with invalid account number...\n";

try {
    $invalidAccountNumber = '**********';

    $response = Http::post($apiUrl, [
        'accountNumber' => $invalidAccountNumber,
        'bankCode' => $bankCode
    ]);

    echo "Status: " . $response->status() . "\n";
    echo "Response: " . $response->body() . "\n\n";

    if ($response->status() === 400) {
        echo "✅ Invalid account number correctly rejected\n";
    } else {
        echo "❌ Unexpected response for invalid account number\n";
    }

} catch (Exception $e) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: Test with missing parameters
echo "3. Testing with missing parameters...\n";

try {
    $response = Http::post($apiUrl, [
        'accountNumber' => $testAccountNumber
        // Missing bankCode
    ]);

    echo "Status: " . $response->status() . "\n";
    echo "Response: " . $response->body() . "\n\n";

    if ($response->status() === 422) {
        echo "✅ Missing parameters correctly validated\n";
    } else {
        echo "❌ Unexpected response for missing parameters\n";
    }

} catch (Exception $e) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 4: Test configuration
echo "4. Testing configuration...\n";

$requiredEnvVars = [
    'OCB_USERNAME',
    'OCB_PASSWORD',
    'OCB_CLIENT_ID',
    'OCB_CLIENT_SECRET',
    'OCB_CLIENT_CERTIFICATE'
];

$missingVars = [];
foreach ($requiredEnvVars as $var) {
    if (empty(env($var))) {
        $missingVars[] = $var;
    }
}

if (empty($missingVars)) {
    echo "✅ All required environment variables are configured\n";
} else {
    echo "❌ Missing environment variables:\n";
    foreach ($missingVars as $var) {
        echo "  - {$var}\n";
    }
}

echo "\n=== Test Complete ===\n";

// Instructions
echo "\nInstructions:\n";
echo "1. Make sure all OCB_* environment variables are configured\n";
echo "2. Replace the test account number with a valid OCB account\n";
echo "3. Run this script: php scripts/test_ocb_lookup.php\n";
echo "4. Check the frontend by navigating to the OCB bank connection page\n";
echo "5. Enter an account number and verify the name appears automatically\n";
