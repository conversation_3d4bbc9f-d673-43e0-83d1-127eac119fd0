<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\Http;

class OcbAccountLookupTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test successful account name lookup
     */
    public function test_successful_account_lookup(): void
    {
        // Mock the OCB API responses
        Http::fake([
            '*/ocb-oauth-provider/oauth2/token' => Http::response([
                'access_token' => 'mock_access_token',
                'token_type' => 'Bearer',
                'expires_in' => 3600,
                'scope' => 'OCB'
            ], 200),

            '*/v1/payment/retrieveOCBRecipientName' => Http::response([
                'trace' => [
                    'clientTransId' => 'test_trans_id',
                    'clientTimestamp' => '*****************'
                ],
                'data' => [
                    'recipientName' => 'NGUYEN VAN A',
                    'accountNumber' => '**********'
                ]
            ], 200)
        ]);

        $response = $this->postJson('/api/banks/lookup-account-name', [
            'accountNumber' => '**********',
            'bankCode' => 'ocb'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'account_name' => 'NGUYEN VAN A'
                ]);
    }

    /**
     * Test account lookup with invalid account number
     */
    public function test_invalid_account_lookup(): void
    {
        // Mock the OCB API responses
        Http::fake([
            '*/ocb-oauth-provider/oauth2/token' => Http::response([
                'access_token' => 'mock_access_token',
                'token_type' => 'Bearer',
                'expires_in' => 3600,
                'scope' => 'OCB'
            ], 200),

            '*/v1/payment/retrieveOCBRecipientName' => Http::response([
                'error' => 'Account not found',
                'message' => 'Invalid account number'
            ], 400)
        ]);

        $response = $this->postJson('/api/banks/lookup-account-name', [
            'accountNumber' => '**********',
            'bankCode' => 'ocb'
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false
                ]);
    }

    /**
     * Test account lookup with missing parameters
     */
    public function test_missing_parameters(): void
    {
        $response = $this->postJson('/api/banks/lookup-account-name', [
            'accountNumber' => '**********'
            // Missing bankCode
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['bankCode']);
    }

    /**
     * Test account lookup with invalid bank code
     */
    public function test_invalid_bank_code(): void
    {
        $response = $this->postJson('/api/banks/lookup-account-name', [
            'accountNumber' => '**********',
            'bankCode' => 'invalid_bank'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['bankCode']);
    }

    /**
     * Test account lookup with short account number
     */
    public function test_short_account_number(): void
    {
        $response = $this->postJson('/api/banks/lookup-account-name', [
            'accountNumber' => '123',
            'bankCode' => 'ocb'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['accountNumber']);
    }

    /**
     * Test account lookup with unsupported bank
     */
    public function test_unsupported_bank_lookup(): void
    {
        $response = $this->postJson('/api/banks/lookup-account-name', [
            'accountNumber' => '**********',
            'bankCode' => 'mb' // MBBank doesn't support lookup
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Ngân hàng này không hỗ trợ tra cứu tên chủ tài khoản.'
                ]);
    }
}
