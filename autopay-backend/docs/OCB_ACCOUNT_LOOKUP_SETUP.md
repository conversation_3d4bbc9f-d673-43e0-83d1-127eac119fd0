# OCB Account Lookup Setup Guide

## Tổng quan

Tính năng tra cứu tên chủ tài khoản OCB cho phép hệ thống tra cứu và hiển thị tên chủ tài khoản khi người dùng nhập số tài khoản OCB. Tính năng này sử dụng chung credentials với hệ thống OCB chính nhưng với scope khác (scope=OCB).

## Cấu hình Environment Variables

Sử dụng chung credentials OCB hiện tại, đảm bảo các environment variables sau đã được cấu hình trong file `.env`:

```env
# OCB Bank (sử dụng chung cho cả main integration và account lookup)
OCB_USERNAME=your_username
OCB_PASSWORD=your_password
OCB_CLIENT_ID=your_client_id
OCB_CLIENT_SECRET=your_client_secret
OCB_BASE_URL=https://api.ocb.com.vn/corporates/partner
OCB_CLIENT_CERTIFICATE="-----BEGIN CERTIFICATE-----
MIIDyTCCArGgAwIBAgIUHxNjXBomVM7MtrflConU6jNms+YwDQYJKoZIhvcNAQEL...
-----END CERTIFICATE-----"
OCB_CLIENT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDTxfHf+GPXmnDh...
-----END PRIVATE KEY-----"
```

## API Endpoints

### 1. Lấy Access Token cho Account Lookup

```bash
curl --location 'https://api.ocb.com.vn/corporates/partner/ocb-oauth-provider/oauth2/token' \
--header 'Content-Type: application/x-www-form-urlencoded' \
--data-urlencode 'grant_type=password' \
--data-urlencode 'username=your_username' \
--data-urlencode 'password=your_password' \
--data-urlencode 'client_id=your_client_id' \
--data-urlencode 'client_secret=your_client_secret' \
--data-urlencode 'scope=OCB'
```

### 2. Tra cứu tên chủ tài khoản

```bash
curl --location 'https://api.ocb.com.vn/corporates/partner/v1/payment/retrieveOCBRecipientName' \
--header 'X-Client-Certificate: YOUR_CLIENT_CERTIFICATE' \
--header 'X-IBM-Client-Id: your_lookup_client_id' \
--header 'X-IBM-Client-Secret: your_lookup_client_secret' \
--header 'X-Signature: GENERATED_SIGNATURE' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer ACCESS_TOKEN' \
--data '{
    "trace": {
        "clientTransId": "unique_transaction_id",
        "clientTimestamp": "*****************"
    },
    "data": {
        "queryInfo": {
            "accountNumber": "**********"
        }
    }
}'
```

## Cách sử dụng trong Frontend

Khi người dùng nhập số tài khoản OCB trong form kết nối ngân hàng:

1. Hệ thống sẽ tự động gọi API tra cứu sau 800ms (debounced)
2. Hiển thị loading state trong khi tra cứu
3. Hiển thị tên chủ tài khoản nếu tra cứu thành công
4. Không hiển thị gì nếu tra cứu thất bại

## Kiến trúc

- **AccessTokenForAccountLookupRequest**: Lấy access token riêng cho lookup
- **RetrieveOcbRecipientNameRequest**: Gọi API tra cứu tên chủ tài khoản
- **OcbLookupConnector**: Connector riêng cho lookup operations
- **OcbStrategy::lookupAccountName()**: Method xử lý logic tra cứu
- **BankController::lookupAccountName()**: API endpoint cho frontend

## Lưu ý bảo mật

- Credentials cho account lookup phải được bảo mật riêng biệt
- Access token được cache và tự động refresh khi hết hạn
- Signature được tạo tự động cho mỗi request
- Tất cả requests đều được log để audit

## Troubleshooting

### Lỗi 401 Unauthorized

- Kiểm tra credentials trong .env
- Đảm bảo scope='OCB' được sử dụng

### Lỗi signature không hợp lệ

- Kiểm tra client certificate
- Đảm bảo private key đúng format

### Không tìm thấy tên chủ tài khoản

- Kiểm tra số tài khoản có đúng format
- Đảm bảo tài khoản tồn tại trong hệ thống OCB
