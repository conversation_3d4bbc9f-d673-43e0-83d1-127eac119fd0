{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_TLS_REJECT_UNAUTHORIZED=0 next dev -p 3000 --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "next lint"}, "dependencies": {"@bprogress/next": "^3.2.12", "@casl/ability": "^6.7.3", "@ebay/nice-modal-react": "^1.2.13", "@faker-js/faker": "9.9.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/query-persist-client-core": "^5.83.0", "@tanstack/react-query": "5.83.0", "@tanstack/react-table": "8.21.3", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/canvas-confetti": "^1.9.0", "@uidotdev/usehooks": "^2.4.1", "@xyflow/react": "^12.8.2", "boring-avatars": "^2.0.0", "canvas-confetti": "^1.9.3", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-flag-icons": "1.5.19", "country-region-data": "^3.1.0", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "format-money-js": "^1.6.3", "framer-motion": "12.23.6", "geist": "1.4.2", "immer": "^10.1.1", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "ldrs": "^1.1.7", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "maskdata": "^1.3.3", "mitt": "^3.0.1", "nanoid": "^5.1.5", "next": "^15.4.2", "next-auth": "^5.0.0-beta.19", "next-intl": "4.3.4", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "node-ray": "^2.1.2", "nuqs": "^2.4.3", "qs": "6.14.0", "radix-ui": "^1.4.2", "react": "19.1.0", "react-confetti": "^6.4.0", "react-day-picker": "^9.8.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-lottie-player": "^2.1.0", "react-markdown": "^10.1.0", "react-metatags-hook": "^2.0.0", "react-phone-number-input": "^3.4.12", "react-pro-sidebar": "^1.1.0", "react-resizable-panels": "^3.0.3", "react-select": "^5.10.2", "react-spinners": "^0.17.0", "react-syntax-highlighter": "^15.6.1", "react-tooltip": "5.29.1", "react-wrap-balancer": "1.1.1", "recharts": "2.15.4", "rehype-pretty-code": "^0.14.1", "remark-gfm": "^4.0.1", "request-ip": "^3.3.0", "sharp": "^0.34.3", "simplex-noise": "^4.0.3", "slugify": "^1.6.6", "sonner": "^2.0.6", "tailwind-children": "^0.5.0", "tailwind-merge": "3.3.1", "tw-animate-css": "^1.3.5", "ulid": "^3.0.1", "ulidx": "^2.4.1", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "zod": "^4.0.5", "zustand": "5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query-devtools": "5.83.0", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "4.17.20", "@types/lodash.defaultsdeep": "^4.6.9", "@types/node": "24.0.15", "@types/qs": "^6.14.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-lottie": "^1.2.10", "@types/react-syntax-highlighter": "^15.5.13", "@types/request-ip": "^0.0.41", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^9.31.0", "eslint-config-next": "^15.4.2", "postcss": "8.5.6", "prettier": "3.6.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "typescript": "5.8.3"}, "overrides": {"react-is": "19.0.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}