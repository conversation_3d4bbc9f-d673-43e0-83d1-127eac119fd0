import * as arrowData from '@/assets/lottie/arrow-right.json'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { zodResolver } from '@hookform/resolvers/zod'
import dynamic from 'next/dynamic'
import { Popover as PopoverRadix } from 'radix-ui'
import { useForm } from 'react-hook-form'
import { MdError } from 'react-icons/md'
import { z } from 'zod'
const Lottie = dynamic(() => import('react-lottie-player'), { ssr: false })

import { createDefaultValues, createDynamicSchema } from '@/app/(panel)/banks/connect/common/helper'
import BankIntroduction from '@/app/(panel)/banks/connect/components/bank-introduction'
import { Icons } from '@/components/icons'
import { Alert, AlertDescription } from '@/components/ui/alert'
import type { BankConfig, BankConnectionField } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import { debounce } from 'lodash'
import { createContext, RefObject, useCallback, useContext, useMemo, useRef, useState } from 'react'
import { toast } from 'sonner'
import { useOnClickOutside } from 'usehooks-ts'
import BankNote from '../../components/bank-note'
import { useStore } from '../../stores/store'
import CreateVa from './create-va'
import Greeting from './greeting'
import Otp from './otp'

// Create Bank Context
interface BankContextType {
  bankCode: string
  bank: BankConfig | null
}

const BankContext = createContext<BankContextType | undefined>(undefined)

export const useBankContext = (): BankContextType => {
  const context = useContext(BankContext)
  if (context === undefined) {
    throw new Error('useBankContext must be used within a BankProvider')
  }
  return context
}

export default function Component({ bankCode, bank }: { bankCode: string; bank: BankConfig | null }) {
  const { setIsShowCreateVaForm, setBankAccount } = useStore()
  const [accountName, setAccountName] = useState<string | null>(null)
  const [isLookingUp, setIsLookingUp] = useState(false)

  const {
    isPending,
    mutate: checkBankAccount,
    isError: isErrorMutation,
    error,
    reset: resetMutationState,
  } = useMutation({
    mutationFn: (data: z.infer<typeof dynamicSchema>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/check-account`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onError: (error) => {
      // toast.error(error.message, {
      //   style: {
      //     width: 'fit-content',
      //     whiteSpace: 'nowrap',
      //   },
      // });
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(data.message ?? 'Kết nối thành công')
        setBankAccount(getAccountData())
        setIsShowCreateVaForm(true)
      } else {
        toast.error(data.message)
      }
    },
  })

  const { mutate: lookupAccountName } = useMutation({
    mutationFn: (accountNumber: string) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/lookup-account-name`, {
        method: 'POST',
        body: JSON.stringify({
          accountNumber,
          bankCode,
        }),
      }),
    onMutate: () => {
      setIsLookingUp(true)
      setAccountName(null)
    },
    onSuccess: (data) => {
      setIsLookingUp(false)
      if (data.success) {
        setAccountName(data.account_name)
      } else {
        setAccountName(null)
      }
    },
    onError: () => {
      setIsLookingUp(false)
      setAccountName(null)
    },
  })

  // Debounced lookup function to avoid too many API calls
  const debouncedLookup = useCallback(
    debounce((accountNumber: string) => {
      if (accountNumber && accountNumber.length >= 6) {
        lookupAccountName(accountNumber)
      } else {
        setAccountName(null)
      }
    }, 800),
    [lookupAccountName]
  )

  const bankRequiredFields = useMemo<BankConnectionField[]>(() => {
    if (!bank) return []

    // Map connection_fields to BankConnectionField format
    const fieldMappings: Record<string, BankConnectionField> = {
      accountNumber: {
        name: 'accountNumber',
        title: 'Số tài khoản',
        description: 'Số tài khoản của bạn tại ngân hàng',
        min_length: 3,
      },
      idCardNumber: {
        name: 'idCardNumber',
        title: 'Số CMND/CCCD',
        description: 'Số chứng minh nhân dân hoặc căn cước công dân',
        min_length: 9,
      },
      phoneNumber: {
        name: 'phoneNumber',
        title: 'Số điện thoại',
        description: 'Số điện thoại đăng ký với ngân hàng',
        min_length: 10,
      },
    }

    return bank.connection_fields.map((fieldName: string) => fieldMappings[fieldName]).filter(Boolean)
  }, [bank])

  const dynamicSchema = useMemo(() => createDynamicSchema(bankRequiredFields), [bankRequiredFields])

  const form = useForm<z.infer<typeof dynamicSchema>>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: createDefaultValues(bankRequiredFields),
  })

  // const onSubmit = (values: Record<BankFieldRequirement['name'], string>) => {
  const onSubmit = () => {
    checkBankAccount(getAccountData())
  }

  const getAccountData = () => {
    const values = form.getValues()
    return {
      phoneNumber: (values.phoneNumber as string) ?? '',
      accountNumber: (values.accountNumber as string) ?? '',
      idCardNumber: (values.idCardNumber as string) ?? '',
      bankCode,
    }
  }

  const refPopover = useRef<HTMLDivElement>(null)

  useOnClickOutside(refPopover as RefObject<HTMLElement>, () => {
    resetMutationState()
  })

  return (
    <>
      <BankIntroduction bank={bank} />
      <div className="text-center">Vui lòng nhập thông tin kết nối để tiếp tục</div>
      <Card className="border-0">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="**:data-[error=true]:text-primary space-y-8 p-4">
            {bankRequiredFields &&
              bankRequiredFields.length > 0 &&
              bankRequiredFields.map((bankField) => (
                <FormField
                  key={bankField.name}
                  control={form.control}
                  name={bankField.name}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{bankField.title}</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          value={(field.value as string) || ''}
                          onChange={(e) => {
                            field.onChange(e)
                            // Trigger account name lookup for accountNumber field
                            if (bankField.name === 'accountNumber') {
                              debouncedLookup(e.target.value)
                            }
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        {bankField.description}
                        {bankField.name === 'accountNumber' && (
                          <div className="mt-2">
                            {isLookingUp && (
                              <div className="flex items-center gap-2 text-sm text-blue-600">
                                <Icons.spinner className="h-3 w-3 animate-spin" />
                                Đang tra cứu tên chủ tài khoản...
                              </div>
                            )}
                            {accountName && !isLookingUp && (
                              <div className="text-sm font-medium text-green-600">✓ Chủ tài khoản: {accountName}</div>
                            )}
                          </div>
                        )}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            <Popover open={isErrorMutation}>
              <PopoverTrigger asChild>
                <Button
                  type="submit"
                  size="sm"
                  className="w-full gap-2"
                  disabled={isPending}>
                  {isPending && <Icons.spinner className="animate-spin" />}
                  Tiếp tục
                  <Lottie
                    className="dark:invert"
                    play
                    animationData={arrowData}
                    style={{ width: 20, height: 20 }}
                  />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                ref={refPopover}
                side="top"
                className="w-fit border-0 p-0">
                <Alert variant="destructive">
                  <MdError />
                  <AlertDescription>{error?.message ?? 'Có lỗi xảy ra, vui lòng thử lại sau'}</AlertDescription>
                </Alert>
                <PopoverRadix.Arrow className="fill-accent" />
              </PopoverContent>
            </Popover>
          </form>
        </Form>
      </Card>
      <BankNote bank={bank} />
      <BankContext.Provider value={{ bankCode, bank }}>
        <CreateVa />
        <Otp />
        <Greeting />
      </BankContext.Provider>
    </>
  )
}
