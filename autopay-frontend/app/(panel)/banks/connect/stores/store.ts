import type { BankConfig } from '@/lib/types/banks'
import { create } from 'zustand'

type BankAccount = {
  phoneNumber: string
  accountNumber: string
  idCardNumber: string
}

type Store = {
  bankAccount: BankAccount
  setBankAccount: (bankAccount: BankAccount) => void
  bankCode: string
  setBankCode: (bankCode: string) => void

  isShowRegisterForm: boolean
  setIsShowRegisterForm: (isShowRegisterForm: boolean) => void

  isShowCreateVaForm: boolean
  setIsShowCreateVaForm: (isShowCreateVaForm: boolean) => void

  isShowOtp: boolean
  setIsShowOtp: (isShowOtp: boolean) => void

  isShowGreeting: boolean
  setIsShowGreeting: (isShowGreeting: boolean) => void
}

export const useStore = create<Store>()((set) => ({
  bankAccount: {
    phoneNumber: '',
    accountNumber: '',
    idCardNumber: '',
  },
  setBankAccount: (bankAccount) => set({ bankAccount }),

  bankCode: '',
  setBankCode: (bankCode: string) => set({ bankCode }),

  isShowRegisterForm: false,
  setIsShowRegisterForm: (isShowRegisterForm: boolean) => set({ isShowRegisterForm }),

  isShowCreateVaForm: false,
  setIsShowCreateVaForm: (isShowCreateVaForm: boolean) => set({ isShowCreateVaForm }),

  isShowOtp: false,
  setIsShowOtp: (isShowOtp: boolean) => set({ isShowOtp }),

  isShowGreeting: false,
  setIsShowGreeting: (isShowGreeting: boolean) => set({ isShowGreeting }),
}))
